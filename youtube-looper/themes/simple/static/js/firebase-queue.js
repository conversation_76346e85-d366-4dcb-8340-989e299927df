// ============================================================================
// FIREBASE QUEUE SHARING FUNCTIONALITY
// ============================================================================

// Global variables for debouncing auto-save
let autoSaveTimeout = null;
const AUTO_SAVE_DELAY = 2000; // 2 seconds delay for auto-save

// Global variable to track if we're viewing a shared queue
let isViewingSharedQueue = false;
window.isViewingSharedQueue = false;

/**
 * Save queue to Firebase Firestore
 * @param {string|null} queueId - Optional queue ID, generates new if null
 * @param {boolean} isAutoUpdate - True if called from auto-update (don't show error messages)
 * @returns {Promise<string|null>} - Queue ID if successful, null if failed
 */
async function saveQueueToFirebase(queueId = null, isAutoUpdate = false) {
  const videoQueue = getVideoQueue();

  if (videoQueue.length === 0) {
    if (queueId && isAutoUpdate) {
      // For auto-updates with empty queue, delete the shared queue
      try {
        const db = getFirebaseDb();
        await db.collection('queues').doc(queueId).delete();
        console.log('🗑️ Deleted empty shared queue:', queueId);
        return queueId;
      } catch (error) {
        console.warn('⚠️ Failed to delete empty shared queue:', error);
        return null;
      }
    } else {
      console.log('Cannot share empty queue');
      return null;
    }
  }

  if (!isFirebaseInitialized()) {
    console.log('Firebase not initialized');
    return null;
  }

  if (!queueId) {
    queueId = generateQueueId();
  }

  const queueData = {
    queue: videoQueue,
    currentIndex: getCurrentVideoIndex(),
    isPlaying: getIsPlaying(),
    timestamp: Date.now(),
    title: `Queue shared on ${new Date().toLocaleDateString()}`
  };

  try {
    if (!isAutoUpdate) {
      console.log('Saving queue to cloud...');
    }

    console.log('Saving to Firebase:', {
      queueId: queueId,
      queueData: queueData
    });

    // Extract queue metadata
    const queueMetadata = extractQueueMetadata(queueData);
    const timestamp = firebase.firestore.FieldValue.serverTimestamp();

    // Prepare document data
    const docData = {
      id: queueId,
      queueData: queueData,
      metadata: {
        title: queueMetadata.title,
        videoCount: queueMetadata.videoCount,
        totalDuration: queueMetadata.totalDuration,
        firstVideoThumbnail: queueMetadata.firstVideoThumbnail,
        createdAt: timestamp,
        lastModified: timestamp,
        viewCount: 0
      }
    };

    // Check if document exists to preserve view count and creation date
    const db = getFirebaseDb();
    const docRef = db.collection('queues').doc(queueId);
    const docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      const existingData = docSnapshot.data();
      docData.metadata.viewCount = existingData.metadata?.viewCount || 0;
      docData.metadata.createdAt = existingData.metadata?.createdAt || timestamp;
      docData.metadata.lastModified = timestamp;
    }

    // Save to Firestore
    await docRef.set(docData);

    console.log('Queue saved to Firebase:', queueId);

    if (!isAutoUpdate) {
      console.log('Queue saved successfully');

      // Copy to clipboard
      try {
        await navigator.clipboard.writeText(queueId);
        console.log('Queue ID copied to clipboard');
      } catch (clipboardError) {
        console.warn('Could not copy to clipboard:', clipboardError);
      }
    }

    return queueId;

  } catch (error) {
    console.error('Error saving queue to Firebase:', error);
    if (!isAutoUpdate) {
      console.log('Failed to save queue to cloud:', error.message);
    }
    return null;
  }
}

/**
 * Load queue from Firebase Firestore
 * @param {string} queueId - Queue ID to load
 * @returns {Promise<boolean>} - True if successful
 */
async function loadQueueFromFirebase(queueId) {
  if (!queueId || queueId.trim() === '') {
    console.log('Invalid Queue ID provided');
    return false;
  }

  if (!isFirebaseInitialized()) {
    console.log('Firebase not initialized');
    return false;
  }

  try {
    console.log('Loading queue from cloud...');

    const db = getFirebaseDb();
    const docRef = db.collection('queues').doc(queueId.trim());
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log('Queue not found:', queueId);
      return false;
    }

    const data = docSnapshot.data();
    const loadedData = data.queueData;



    // Load the queue data
    setVideoQueue(loadedData.queue || []);
    setCurrentVideoIndex(loadedData.currentIndex || 0);
    setIsPlaying(false); // Don't auto-play loaded queues

    // Ensure currentVideoIndex is within bounds
    if (getCurrentVideoIndex() >= getVideoQueue().length) {
      setCurrentVideoIndex(0);
    }

    // Set the loaded queue ID as current queue ID so updates sync back to shared queue
    try {
      sessionStorage.setItem('currentQueueId', queueId.trim());
      console.log('🔗 Set current queue ID to loaded queue:', queueId.trim());
    } catch (error) {
      console.warn('Could not save queue ID to session storage:', error);
    }

    // Mark that we're now viewing a shared queue
    isViewingSharedQueue = true;
    window.isViewingSharedQueue = true;
    console.log('🔗 Now viewing shared queue - personal queue auto-save disabled');

    // Increment view count
    await docRef.update({
      'metadata.viewCount': firebase.firestore.FieldValue.increment(1)
    });

    // Update UI
    updateQueueDisplay();
    updatePlayerControls();
    saveQueueToStorage(); // Save to local storage too

    // Update queue link display to show the loaded queue's link
    if (typeof updateQueueLinkDisplay === 'function') {
      updateQueueLinkDisplay();
    }

    const loadedDate = data.metadata?.lastModified?.toDate?.() || new Date();
    console.log(`Loaded ${getVideoQueue().length} video(s) from shared queue`);

    return true;

  } catch (error) {
    console.error('Error loading queue from Firebase:', error);
    console.log('Failed to load queue from cloud:', error);
    return false;
  }
}

/**
 * List queues from Firebase Firestore
 * @param {string} sortBy - Sort criteria ('recent', 'popular', 'newest', 'longest')
 * @param {number} limit - Maximum number of queues to return
 * @returns {Promise<Object>} - Result object with success status and data
 */
async function listQueuesFromFirebase(sortBy = 'recent', limit = 50) {
  try {
    if (!isFirebaseInitialized()) {
      throw new Error('Firebase not initialized');
    }

    const db = getFirebaseDb();
    let query = db.collection('queues');

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        query = query.orderBy('metadata.viewCount', 'desc');
        break;
      case 'recent':
        query = query.orderBy('metadata.lastModified', 'desc');
        break;
      case 'newest':
        query = query.orderBy('metadata.createdAt', 'desc');
        break;
      case 'longest':
        query = query.orderBy('metadata.totalDuration', 'desc');
        break;
      default:
        query = query.orderBy('metadata.lastModified', 'desc');
    }

    // Apply limit
    query = query.limit(limit);

    const querySnapshot = await query.get();
    const queues = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const queueData = data.queueData || {};
      const metadata = data.metadata || {};

      queues.push({
        queueId: doc.id,
        lastModified: metadata.lastModified?.toDate?.() || new Date(),
        title: metadata.title || 'Untitled Queue',
        viewCount: metadata.viewCount || 0,
        createdDate: metadata.createdAt?.toDate?.() || new Date(),
        videoCount: metadata.videoCount || 0,
        totalDuration: metadata.totalDuration || 0,
        thumbnail: metadata.firstVideoThumbnail || '',
        preview: queueData.queue ? queueData.queue.slice(0, 3).map(v => ({
          title: v.title,
          thumbnail: v.thumbnail,
          duration: v.duration
        })) : []
      });
    });

    console.log('Queues listed from Firebase:', queues.length);
    return {
      success: true,
      message: 'Queues listed successfully',
      data: {
        queues: queues,
        total: queues.length,
        sortBy: sortBy,
        limit: limit
      }
    };

  } catch (error) {
    console.error('Error listing queues from Firebase:', error);
    return {
      success: false,
      message: 'Failed to list queues: ' + error.message
    };
  }
}

// ============================================================================
// PERSONAL QUEUE AUTO-SAVE FUNCTIONS (replaces localStorage)
// ============================================================================

/**
 * Auto-save queue to Firebase personal queue
 * Debounced to avoid excessive Firebase writes
 * @param {boolean} immediate - If true, save immediately without debouncing
 */
function saveQueueToStorage(immediate = false) {
  if (immediate) {
    // Save immediately for critical operations like adding items
    console.log('🚀 Immediate Firebase sync triggered');

    // NOTE: Personal queue auto-save disabled to prevent unwanted queue creation
    // The current queue is a working queue, not a saved personal queue
    // Personal queues should only be created explicitly by the user

    // Only update shared queue if it exists (for sharing functionality)
    if (typeof autoUpdateSharedQueue === 'function') {
      autoUpdateSharedQueue().then(() => {
        console.log('✅ Shared queue immediately synced to Firebase');
      }).catch(error => {
        console.warn('❌ Could not immediately update shared queue:', error);
      });
    }
  } else {
    // NOTE: Debounced personal queue auto-save also disabled
    // Clear existing timeout if any
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Only update shared queue if it exists
    if (typeof autoUpdateSharedQueue === 'function') {
      autoSaveTimeout = setTimeout(async () => {
        try {
          await autoUpdateSharedQueue();
        } catch (error) {
          console.warn('Could not auto-update shared queue:', error);
        }
      }, AUTO_SAVE_DELAY);
    }
  }

  // Show storage indicator briefly
  const indicator = document.getElementById('storage-indicator');
  if (indicator && getVideoQueue().length > 0) {
    indicator.classList.remove('show');
    // Force reflow to restart animation
    indicator.offsetHeight;
    indicator.classList.add('show');
  }
}

/**
 * Auto-save personal queue to Firebase
 * NOTE: This function is no longer used for automatic queue saving to prevent
 * unwanted personal queue creation. It's kept for compatibility and explicit
 * personal queue operations (like switching modes).
 * @returns {Promise<boolean>} - True if successful
 */
async function autoSavePersonalQueue() {
  console.log('⚠️ autoSavePersonalQueue called - this function is disabled to prevent unwanted queue creation');
  console.log('💡 Current queue sync is handled by autoUpdateSharedQueue for sharing functionality');
  console.log('📝 Personal queues should be created explicitly via the queue creation interface');

  // Return true to avoid breaking existing code that expects this function to work
  return true;

  // Original implementation commented out to prevent unwanted personal queue creation:
  /*
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized for auto-save');
    return false;
  }

  // Don't save personal queue if we're viewing a shared queue
  if (isViewingSharedQueue) {
    console.log('🔗 Skipping personal queue save - viewing shared queue');
    return false;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    console.warn('No user authenticated, cannot save personal queue');
    return false;
  }

  const videoQueue = getVideoQueue();
  const queueData = {
    queue: videoQueue,
    currentIndex: getCurrentVideoIndex(),
    isPlaying: getIsPlaying(),
    timestamp: Date.now(),
    title: `Personal queue for ${userId}`
  };

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);

    if (videoQueue.length === 0) {
      // Delete the document if queue is empty
      await docRef.delete();
      console.log('✅ Empty personal queue deleted from Firebase');
    } else {
      // Save the queue data
      await docRef.set({ queueData: queueData });
      console.log('✅ Personal queue auto-saved to Firebase for user:', userId);
    }

    return true;
  } catch (error) {
    console.error('❌ Error auto-saving personal queue:', error);
    return false;
  }
  */
}

/**
 * Load queue from Firebase using user's personal queue
 */
async function loadQueueFromStorage() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized, cannot load queue');
    return;
  }

  // Don't load personal queue if we're viewing a shared queue
  if (isViewingSharedQueue) {
    console.log('🔗 Skipping personal queue load - viewing shared queue');
    return;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    console.log('No user authenticated, starting with empty queue');
    return;
  }

  try {
    console.log('🔄 Loading personal queue from Firebase for user:', userId);
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log('No personal queue found, starting with empty queue');
      return;
    }

    const data = docSnapshot.data();
    const loadedData = data.queueData;

    // Check if data is not too old (optional: expire after 7 days)
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    if (Date.now() - loadedData.timestamp > maxAge) {
      console.log('Personal queue data too old, starting with empty queue');
      return;
    }

    // Load the queue data
    setVideoQueue(loadedData.queue || []);
    setCurrentVideoIndex(loadedData.currentIndex || 0);
    setIsPlaying(false); // Don't auto-play on load

    // Ensure currentVideoIndex is within bounds
    if (getCurrentVideoIndex() >= getVideoQueue().length) {
      setCurrentVideoIndex(0);
    }

    console.log(`✅ Loaded ${getVideoQueue().length} video(s) from personal queue`);

    // Update UI
    updateQueueDisplay();
    updatePlayerControls();

  } catch (error) {
    console.warn('Could not load personal queue from Firebase:', error);
  }
}

/**
 * Clear personal queue from Firebase
 * NOTE: This function is mainly for cleaning up old-style personal queue documents
 * that may have been created before the auto-save was disabled.
 */
async function clearQueueFromStorage() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized, cannot clear personal queue');
    return;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    console.warn('No user authenticated, cannot clear personal queue');
    return;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);

    // Check if the document exists before trying to delete it
    const docSnapshot = await docRef.get();
    if (docSnapshot.exists) {
      await docRef.delete();
      console.log('✅ Old-style personal queue cleared from Firebase for user:', userId);
    } else {
      console.log('ℹ️ No old-style personal queue found to clear for user:', userId);
    }
  } catch (error) {
    console.warn('Could not clear personal queue from Firebase:', error);
  }
}

/**
 * Check if there's a saved personal queue in Firebase
 * @returns {Promise<boolean>} - True if saved data exists
 */
async function hasSavedQueue() {
  if (!isFirebaseInitialized()) {
    return false;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    return false;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();
    return docSnapshot.exists;
  } catch (error) {
    console.warn('Could not check for saved personal queue:', error);
    return false;
  }
}

/**
 * Get saved personal queue data without loading it
 * @returns {Promise<Object|null>} - Saved queue data or null
 */
async function getSavedQueueData() {
  if (!isFirebaseInitialized()) {
    return null;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    return null;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return null;
    }

    const data = docSnapshot.data();
    const queueData = data.queueData;

    // Check if data is not too old
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    if (Date.now() - queueData.timestamp > maxAge) {
      return null;
    }

    return queueData;
  } catch (error) {
    console.warn('Could not get saved personal queue data:', error);
    return null;
  }
}

/**
 * Save a named personal queue to Firebase
 * @param {string} userId - User ID
 * @param {string} queueTitle - Title for the queue
 * @returns {Promise<string|null>} - Queue ID if successful, null if failed
 */
async function savePersonalQueueToFirebase(userId, queueTitle = null) {
  console.log('💾 savePersonalQueueToFirebase called with:', { userId, queueTitle });

  const videoQueue = getVideoQueue();
  console.log('📋 Current video queue:', videoQueue);

  if (videoQueue.length === 0) {
    console.log('❌ Cannot save empty queue');
    console.log('Cannot save empty queue');
    return null;
  }

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return null;
  }

  if (!userId) {
    console.log('❌ No user ID provided');
    console.log('User not signed in');
    return null;
  }

  // Generate a unique ID for this personal queue
  const queueId = `personal_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  console.log('🆔 Generated queue ID:', queueId);

  // Use provided title or generate one
  const title = queueTitle || `Personal Queue - ${new Date().toLocaleDateString()}`;
  console.log('📝 Queue title:', title);

  const queueData = {
    queue: videoQueue,
    currentIndex: getCurrentVideoIndex(),
    isPlaying: getIsPlaying(),
    timestamp: Date.now(),
    title: title
  };

  try {
    const db = getFirebaseDb();
    const timestamp = firebase.firestore.FieldValue.serverTimestamp();

    // Prepare document data for personal queue
    const docData = {
      id: queueId,
      userId: userId,
      queueData: queueData,
      lastModified: timestamp,
      createdAt: timestamp,
      isPersonal: true
    };

    console.log('💾 Saving document data:', docData);

    // Save to personal_queues collection
    const docRef = db.collection('personal_queues').doc(queueId);
    await docRef.set(docData);

    console.log('✅ Personal queue saved to Firebase:', queueId);

    // Invalidate personal queues cache since new queue was added
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    return queueId;

  } catch (error) {
    console.error('❌ Error saving personal queue to Firebase:', error);
    console.log('Failed to save personal queue:', error.message);
    return null;
  }
}

/**
 * Switch to personal queue mode (stop viewing shared queue)
 */
async function switchToPersonalMode() {
  isViewingSharedQueue = false;
  window.isViewingSharedQueue = false;

  // Clear the shared queue ID
  try {
    sessionStorage.removeItem('currentQueueId');
  } catch (error) {
    console.warn('Could not clear queue ID from session storage:', error);
  }

  console.log('👤 Switched to personal mode - personal queue auto-save enabled');

  // Load personal queue from Firebase
  try {
    await loadQueueFromStorage();
    console.log('✅ Personal queue loaded');
  } catch (error) {
    console.warn('Could not load personal queue:', error);
    // Clear the queue if we can't load personal queue
    if (typeof setVideoQueue === 'function') {
      setVideoQueue([]);
      setCurrentVideoIndex(0);
    }
  }

  // Update UI
  if (typeof updateQueueDisplay === 'function') {
    updateQueueDisplay();
  }
  if (typeof updatePlayerControls === 'function') {
    updatePlayerControls();
  }
  if (typeof updateQueueLinkDisplay === 'function') {
    updateQueueLinkDisplay();
  }
}

/**
 * Check if currently viewing a shared queue
 * @returns {boolean} - True if viewing shared queue
 */
function isInSharedMode() {
  return isViewingSharedQueue;
}

/**
 * Save draft queue as a personal queue
 * @param {string} title - Queue title
 * @param {Array} draftQueue - Draft queue videos
 * @returns {Promise<string|null>} - Queue ID if successful
 */
async function savePersonalQueueFromDraft(title, draftQueue) {
  console.log('💾 savePersonalQueueFromDraft called with:', { title, draftQueue });

  if (!draftQueue || draftQueue.length === 0) {
    console.log('❌ Cannot save empty draft queue');
    console.log('Cannot save empty draft queue');
    return null;
  }

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return null;
  }

  // Get current user
  const auth = getFirebaseAuth();
  const user = auth.currentUser;

  if (!user) {
    console.log('❌ No authenticated user');
    console.log('User not authenticated');
    return null;
  }

  const userId = user.uid;

  // Generate a unique ID for this personal queue
  const queueId = `personal_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  console.log('🆔 Generated queue ID:', queueId);

  const queueData = {
    queue: draftQueue,
    currentIndex: 0,
    isPlaying: false,
    timestamp: Date.now(),
    title: title
  };

  try {
    const db = getFirebaseDb();
    const timestamp = firebase.firestore.FieldValue.serverTimestamp();

    // Prepare document data for personal queue
    const docData = {
      id: queueId,
      userId: userId,
      queueData: queueData,
      lastModified: timestamp,
      createdAt: timestamp,
      isPersonal: true,
      isPublic: false
    };

    console.log('💾 Saving document data:', docData);

    // Save to personal_queues collection
    const docRef = db.collection('personal_queues').doc(queueId);
    await docRef.set(docData);

    console.log('✅ Personal queue saved to Firebase:', queueId);

    // Invalidate personal queues cache since new queue was added
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    console.log(`Queue "${title}" saved successfully`);
    return queueId;

  } catch (error) {
    console.error('❌ Error saving personal queue to Firebase:', error);
    console.log('Failed to save queue:', error.message);
    return null;
  }
}

/**
 * Load and play a personal queue
 * @param {string} queueId - Queue ID to load and play
 */
async function loadPersonalQueueAndPlay(queueId) {
  console.log('🎵 loadPersonalQueueAndPlay called with:', queueId);

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return false;
  }

  // Get current user
  const auth = getFirebaseAuth();
  const user = auth.currentUser;

  if (!user) {
    console.log('❌ No authenticated user');
    console.log('User not authenticated');
    return false;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(queueId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log('❌ Queue not found:', queueId);
      console.log('Queue not found:', queueId);
      return false;
    }

    const data = docSnapshot.data();
    const queueData = data.queueData;

    if (!queueData || !queueData.queue || queueData.queue.length === 0) {
      console.log('❌ Queue data is empty');
      console.log('Queue data is empty');
      return false;
    }

    // Load the queue into current playing queue
    setVideoQueue(queueData.queue);
    setCurrentVideoIndex(0);
    setIsPlaying(false);

    // Update UI
    updateQueueDisplay();
    updatePlayerControls();
    saveQueueToStorage();

    // Auto-play the first video
    setTimeout(() => {
      playQueue();
    }, 500);

    console.log('✅ Personal queue loaded and playing:', queueData.title);
    console.log('Queue loaded and playing');
    return true;

  } catch (error) {
    console.error('❌ Error loading personal queue:', error);
    console.log('Failed to load queue:', error.message);
    return false;
  }
}

console.log('✅ Firebase Queue module loaded');
